#!/usr/bin/env python3

import re

# Read the program
with open('program.txt', 'r') as f:
    program = f.read().strip()

# Find all _F variables
f_vars = re.findall(r'_F(\d+)', program)
print(f"Found {len(f_vars)} _F variable references")

# Analyze the pattern around each _F variable
bit_patterns = {}

# Look for the pattern that indicates a bit should be 1
# This is the complex expression: (((S ((S I) (K (K I)))) (K K)) _F...)
pattern_1 = r'\(\(\(S \(\(S I\) \(K \(K I\)\)\)\) \(K K\)\) _F(\d+)\)'
matches_1 = re.findall(pattern_1, program)
print(f"Found {len(matches_1)} bits expected to be 1")

for bit_num in matches_1:
    bit_patterns[int(bit_num)] = 1

# All other _F variables should be 0
for bit_num in f_vars:
    bit_num = int(bit_num)
    if bit_num not in bit_patterns:
        bit_patterns[bit_num] = 0

print(f"Total bit patterns: {len(bit_patterns)}")

# Try different bit interpretations
def bits_to_char(bits, reverse_bits=False, reverse_bytes=False):
    if reverse_bits:
        bits = bits[::-1]

    char_value = 0
    for bit in bits:
        char_value = (char_value << 1) | bit

    if reverse_bytes:
        # Reverse the byte order
        char_value = ((char_value & 0x0F) << 4) | ((char_value & 0xF0) >> 4)

    return char_value

def reconstruct_flag(bit_patterns, reverse_bit_order=False, reverse_byte_order=False):
    flag_chars = []
    for char_idx in range(70):  # 70 characters
        char_bits = []
        for bit_idx in range(8):  # 8 bits per character
            if reverse_bit_order:
                global_bit_idx = char_idx * 8 + (7 - bit_idx)
            else:
                global_bit_idx = char_idx * 8 + bit_idx

            if global_bit_idx in bit_patterns:
                char_bits.append(bit_patterns[global_bit_idx])
            else:
                char_bits.append(0)

        char_value = bits_to_char(char_bits, reverse_bytes=reverse_byte_order)

        if 32 <= char_value <= 126:  # printable ASCII
            flag_chars.append(chr(char_value))
        else:
            flag_chars.append(f'\\x{char_value:02x}')

    return ''.join(flag_chars)

# Try different combinations
print("Trying different bit interpretations:")
print(f"Normal: {reconstruct_flag(bit_patterns, False, False)[:20]}...")
print(f"Reverse bit order: {reconstruct_flag(bit_patterns, True, False)[:20]}...")
print(f"Reverse byte order: {reconstruct_flag(bit_patterns, False, True)[:20]}...")
print(f"Both reversed: {reconstruct_flag(bit_patterns, True, True)[:20]}...")

# The one that starts with 'i' looks promising, let's see more of it
flag_candidate = reconstruct_flag(bit_patterns, True, False)
print(f"\nFull flag candidate (reverse bit order): {flag_candidate}")

# Let's check what the expected bits should be for "idek{"
expected_start = "idek{"
print(f"\nExpected bits for '{expected_start}':")
for i, char in enumerate(expected_start):
    char_bits = []
    char_val = ord(char)
    for bit_idx in range(8):
        bit = (char_val >> (7 - bit_idx)) & 1
        char_bits.append(str(bit))
    print(f"'{char}' (0x{char_val:02x}): {''.join(char_bits)}")

print(f"\nActual bits (first 40 bits):")
for i in range(min(40, 560)):
    if i in bit_patterns:
        print(bit_patterns[i], end='')
    else:
        print('?', end='')
    if (i + 1) % 8 == 0:
        print(' ', end='')
print()

# Let's also try LSB first interpretation
def reconstruct_flag_lsb(bit_patterns):
    flag_chars = []
    for char_idx in range(70):  # 70 characters
        char_value = 0
        for bit_idx in range(8):  # 8 bits per character, LSB first
            global_bit_idx = char_idx * 8 + bit_idx
            if global_bit_idx in bit_patterns and bit_patterns[global_bit_idx]:
                char_value |= (1 << bit_idx)

        if 32 <= char_value <= 126:  # printable ASCII
            flag_chars.append(chr(char_value))
        else:
            flag_chars.append(f'\\x{char_value:02x}')

    return ''.join(flag_chars)

flag_lsb = reconstruct_flag_lsb(bit_patterns)
print(f"\nFlag with LSB first: {flag_lsb[:20]}...")
print(f"Full LSB flag: {flag_lsb}")
